/**
 * 翻译相关的Action函数
 */

import { updateIconWithTranslation } from '@src/contents/scripts/injectTranslate';
import type { BaseAction } from './index'

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    handler: (response: any, group: any[]) => {
      console.log('handler', response);
      if (response.endFlag) {
        //
      }

      // 使用'###'分割批量翻译结果
      const regex = /翻译结果：(.*?)(?=[^\\]")/;
      const match = response.result.match(regex);
      if (match) {
        const translation = match[1].trim();
        const results = response.result.split('###');

        group.forEach((item: any, index: number) => {
          if (results[index]) {
            updateIconWithTranslation(
              item.icon,
              results[index],
              item.paragraph,
              item.htmlStructure
            )
          }
        });
      }
      else {
        // 处理错误情况
        console.error('翻译失败:', response?.error || '未知错误');
      }
    }
  };
};

/**
 * 获取所有翻译相关的actions
 */
export const getTranslationActions = (): BaseAction[] => {
  return [
    createInsertDomAction()
  ];
};